import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/services/app_settings/controller/settings_controller.dart';
import 'package:opti4t_tasks/src/core/theme/theme_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/views/login/login_screen.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/home_screen.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseApp extends HookConsumerWidget {
  const BaseApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);

    final themeController = ref.watch(themeProvider);

    //settingsController.locale.languageCode == 'en'
    //         ? GoogleFonts.poppinsTextTheme()
    //         :
    final textTheme = GoogleFonts.alexandriaTextTheme();

    return BaseMaterialApp(
      //? Localization
      locale: settingsController.locale,
      supportedLocales: AppConsts.supportedLocales,
      localizationsDelegates: AppConsts.localizationsDelegates,
      //? Theme
      themeMode: ThemeMode.dark,
      theme: themeController.appTheme(
        textTheme: textTheme,
      ),
      home: GetStorageService.hasData(key: LocalKeys.user)
          ? const HomeScreen()
          : const LoginScreen(),
      title: AppConsts.appName,
    );
  }
}
